// Global variables

let rawMovements = [];
let processedMovements = [];
let rawCashMovements = [];
let processedCashMovements = [];
let currentMonthCashTotals = { fbTotal: 0, cbRcTotal: 0, balance: 0 };
let expandedCompanies = new Set();
let expandedCashCompanies = new Set();
let activeTab = 'credito';
var datosUsuario = null
//#region Initialization
document.addEventListener("sessionReady", () => {
    init();
});

async function init() {
    try {
        showLoading();
        
        datosUsuario = session = await getCookieValue("session");
        await cargarDatosUsuario();
        
        // showMainContent() se llamará desde cargarDatosUsuario() si es exitoso
    } catch (error) {
        console.error('Error in init:', error);
        showError('Error al inicializar la aplicación');
    }
}

async function cargarDatosUsuario() {
    let nrodoc = datosUsuario.NRO_DOCUMENTO;
    const estadoActual = await consultarUsuarioEstadoCuenta(nrodoc);

    if (!estadoActual) {
        return false; 
    }


    let ctacte = datosUsuario.COD_CTACTE;

    const parametros = {
        "@CodCtaCte": ctacte,
    };

    let paramsBl = {
        "@CodCtaCte" : ctacte,
        "@NroDoc": nrodoc
    }

    try {

        const datosBl = await clickFunciones.callWebserviceSql("SP_GET_INFO_COLAB_APP", paramsBl);
        if (datosBl) {
            datosUsuario.LIMITE_CREDITO = datosBl.LimiteCredito ;
            let estadoCtaCte = datosBl.EstadoCtaCteCodigo;

            if (estadoCtaCte && estadoCtaCte != "2") {
                showInactiveState();
                return false;
            }

        } else {
            datosUsuario.LIMITE_CREDITO = 0;
         
        }

        const datosCredito = await clickFunciones.callWebserviceSql("SP_GET_ESTADOCTE_X_CLIE", parametros);

        console.log('Datos crédito:', datosCredito);
        if (datosCredito && datosCredito.success && datosCredito.data && datosCredito.data.length > 0) {
            rawMovements = datosCredito.data;
            processedMovements = processMovementData(rawMovements);
            console.log('Processed movements:', processedMovements);
        } else {
            // No hay datos de crédito válidos
            rawMovements = [];
            processedMovements = [];
            console.log('No hay datos de crédito válidos');
        }

        const datosContado = await clickFunciones.callWebserviceSql("SP_GET_ESTADO_CONTADO_X_CLIE", parametros);

        if (datosContado && datosContado.success && datosContado.data) {
            rawCashMovements = datosContado.data;
            const cashResult = processCashMovementData(rawCashMovements);
            processedCashMovements = cashResult.processedGroups;
            currentMonthCashTotals = cashResult.currentMonthTotals;
        } else {
            // No hay datos de contado válidos
            rawCashMovements = [];
            processedCashMovements = [];
            currentMonthCashTotals = { fbTotal: 0, cbRcTotal: 0, balance: 0 };
        }
    
        renderCreditTab();
        renderCashTab();
        showMainContent();


        return true; 

    } catch (error) {
        console.error('Error loading user data:', error);
        console.error('Stack trace:', error.stack);
        return false; 
    }
}
//#endregion

async function consultarUsuarioEstadoCuenta(nrodoc) {
    try {

        let parametros = {
            PALIAS: nrodoc
        }

        const dts_form = await clickFunciones.callWebservice("PROC_GET_USUARIO_ESTADO_BASLAB", parametros);
        let datos = dts_form[0];

        if (datos) {

            let resultado = datos.RESULTADO;

            if (resultado == "1") {

                let codctacte = datos.COD_CTACTE;
                let codlegajo = datos.COD_LEGAJO;

                if (typeof updateSessionCookies === 'function') {
                    updateSessionCookies({
                        COD_CTACTE: codctacte,
                        COD_LEGAJO: codlegajo
                    });
                }

                datosUsuario.COD_CTACTE = codctacte;
                datosUsuario.COD_LEGAJO = codlegajo;
                datosUsuario = await getCookieValue("session");

                return true; // Usuario activo

            } else {
              
           
                if (typeof updateSessionCookies === 'function') {
                    updateSessionCookies({
                        COD_CTACTE: 0,
                        COD_LEGAJO: 0
                    });
                }

                datosUsuario.COD_CTACTE = 0;
                datosUsuario.COD_LEGAJO = 0;
                datosUsuario = await getCookieValue("session");

                showInactiveState();
                return false; 
            }
        }

        showInactiveState();
        return false;

    } catch (error) {
        console.error('Error al consultar estado del usuario:', error);
        showInactiveState();
        return false;
    }
}

// Función para el botón "Verificar Estado"
async function verificarEstadoCuenta() {
    const nrodoc = datosUsuario.NRO_DOCUMENTO;
    if (nrodoc) {

        showLoading(); 
        const estadoActual = await consultarUsuarioEstadoCuenta(nrodoc);

        if (estadoActual) {
            await cargarDatosUsuario();
        }


    } else {
        console.error('No se encontró el número de documento');
        showInactiveState();
    }
}


function updateSessionCookies(newData) {
    try {

        const sessionCookie = getCookieByName('session');

        if (sessionCookie) {
    
            const decodedSession = decodeURIComponent(sessionCookie);
            const sessionData = JSON.parse(decodedSession);

            if (newData.COD_CTACTE != undefined || newData.COD_CTACTE != "0" || newData.COD_CTACTE != null) {
                sessionData.COD_CTACTE = newData.COD_CTACTE;
            }
            if (newData.COD_LEGAJO != undefined || newData.COD_LEGAJO != "0" || newData.COD_LEGAJO != null ) {
                sessionData.COD_LEGAJO = newData.COD_LEGAJO;
            }

            const updatedSessionJson = JSON.stringify(sessionData);
            const encodedSession = encodeURIComponent(updatedSessionJson);

            document.cookie = `session=${encodedSession}; path=/`;

        } 

    } catch (error) {
        console.error('Error al actualizar la cookie "session":', error);
    }
}

// Función auxiliar para obtener una cookie por nombre
function getCookieByName(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        return parts.pop().split(';').shift();
    }
    return null;
}


//#region Data Processing Functions
function processMovementData(movements) {

    if (!movements || movements.length === 0) {
        return [];
    }

    const groupedByCompany = movements.reduce((acc, movement) => {
        const companyCode = movement.CODEMP;
        if (!acc[companyCode]) {
            acc[companyCode] = {
                companyCode,
                companyName: movement.NOMBRE_EMPRESA,
                movements: []
            };
        }
        acc[companyCode].movements.push(movement);
        return acc;
    }, {});

    const result = Object.values(groupedByCompany).map(group => {
        const allMovements = group.movements;
        const matchedInvoices = new Map();

        allMovements.forEach(movement => {
            if (movement.TIPO_COMPROBANTE === 'CB' || movement.TIPO_COMPROBANTE === 'RC') {
                const matchingFactura = allMovements.find(
                    m => m.TIPO_COMPROBANTE === 'FB' &&
                         m.PREFIJO === movement.PREFIJO &&
                         m.NUMERO === movement.NUMERO &&
                         !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`)
                );

                if (matchingFactura) {
                    matchedInvoices.set(`${matchingFactura.PREFIJO}-${matchingFactura.NUMERO}`, true);
                }
            }
        });

        // Calculate totals
        const fbTotal = allMovements
            .filter(m => m.TIPO_COMPROBANTE === 'FB' && !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`))
            .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES || 0), 0);

        const cbRcTotal = allMovements
            .filter(m => (m.TIPO_COMPROBANTE === 'CB' || m.TIPO_COMPROBANTE === 'RC'))
            .reduce((sum, m) => {
                const hasMatchingInvoice = allMovements.some(
                    inv => inv.TIPO_COMPROBANTE === 'FB' &&
                          inv.PREFIJO === m.PREFIJO &&
                          inv.NUMERO === m.NUMERO
                );
                return hasMatchingInvoice ? sum : sum + parseFloat(m.TOTAL_EN_DOLARES || 0);
            }, 0);

        return {
            companyCode: group.companyCode,
            companyName: group.companyName,
            fbTotal,
            cbRcTotal,
            balance: fbTotal - cbRcTotal,
            totalAmount: allMovements.reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES || 0), 0),
            movements: group.movements
        };
    });

    return result;
}

function processCashMovementData(movements) {
    if (!movements || movements.length === 0) {
        return {
            processedGroups: [],
            currentMonthTotals: { fbTotal: 0, cbRcTotal: 0, balance: 0 }
        };
    }

    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    let currentMonthFbTotal = 0;
    let currentMonthCbRcTotal = 0;

    const groupedByCompany = movements.reduce((acc, movement) => {
        const companyCode = movement.CODEMP;
        if (!acc[companyCode]) {
            acc[companyCode] = {
                companyCode,
                companyName: movement.NOMBRE_EMPRESA,
                movements: [],
                currentMonthMovements: []
            };
        }

        acc[companyCode].movements.push(movement);

        const movementDate = new Date(movement.FECHA);
        if (movementDate.getMonth() === currentMonth && movementDate.getFullYear() === currentYear) {
            acc[companyCode].currentMonthMovements.push(movement);
        }

        return acc;
    }, {});

    const processedGroups = Object.values(groupedByCompany).map(group => {
        const allMovements = group.movements;
        const matchedInvoices = new Map();

        allMovements.forEach(movement => {
            if (movement.TIPO_COMPROBANTE === 'CB' || movement.TIPO_COMPROBANTE === 'RC') {
                const matchingFactura = allMovements.find(
                    m => m.TIPO_COMPROBANTE === 'FB' &&
                         m.PREFIJO === movement.PREFIJO &&
                         m.NUMERO === movement.NUMERO &&
                         !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`)
                );

                if (matchingFactura) {
                    matchedInvoices.set(`${matchingFactura.PREFIJO}-${matchingFactura.NUMERO}`, true);
                }
            }
        });

        const fbTotal = allMovements
            .filter(m => m.TIPO_COMPROBANTE === 'FB' && !matchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`))
            .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES || 0), 0);

        const cbRcTotal = allMovements
            .filter(m => (m.TIPO_COMPROBANTE === 'CB' || m.TIPO_COMPROBANTE === 'RC'))
            .reduce((sum, m) => {
                const hasMatchingInvoice = allMovements.some(
                    inv => inv.TIPO_COMPROBANTE === 'FB' &&
                          inv.PREFIJO === m.PREFIJO &&
                          inv.NUMERO === m.NUMERO
                );
                return hasMatchingInvoice ? sum : sum + parseFloat(m.TOTAL_EN_DOLARES || 0);
            }, 0);

        // Calculate current month totals
        const currentMonthMovements = group.currentMonthMovements;
        const currentMonthMatchedInvoices = new Map();

        currentMonthMovements.forEach(movement => {
            if (movement.TIPO_COMPROBANTE === 'CB' || movement.TIPO_COMPROBANTE === 'RC') {
                const matchingFactura = currentMonthMovements.find(
                    m => m.TIPO_COMPROBANTE === 'FB' &&
                         m.PREFIJO === movement.PREFIJO &&
                         m.NUMERO === movement.NUMERO &&
                         !currentMonthMatchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`)
                );

                if (matchingFactura) {
                    currentMonthMatchedInvoices.set(`${matchingFactura.PREFIJO}-${matchingFactura.NUMERO}`, true);
                }
            }
        });

        const currentMonthFb = currentMonthMovements
            .filter(m => m.TIPO_COMPROBANTE === 'FB' && !currentMonthMatchedInvoices.has(`${m.PREFIJO}-${m.NUMERO}`))
            .reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES || 0), 0);

        const currentMonthCbRc = currentMonthMovements
            .filter(m => (m.TIPO_COMPROBANTE === 'CB' || m.TIPO_COMPROBANTE === 'RC'))
            .reduce((sum, m) => {
                const hasMatchingInvoice = currentMonthMovements.some(
                    inv => inv.TIPO_COMPROBANTE === 'FB' &&
                          inv.PREFIJO === m.PREFIJO &&
                          inv.NUMERO === m.NUMERO
                );
                return hasMatchingInvoice ? sum : sum + parseFloat(m.TOTAL_EN_DOLARES || 0);
            }, 0);

        currentMonthFbTotal += currentMonthFb;
        currentMonthCbRcTotal += currentMonthCbRc;

        return {
            companyCode: group.companyCode,
            companyName: group.companyName,
            fbTotal,
            cbRcTotal,
            balance: fbTotal - cbRcTotal,
            totalAmount: allMovements.reduce((sum, m) => sum + parseFloat(m.TOTAL_EN_DOLARES || 0), 0),
            currentMonthBalance: currentMonthFb - currentMonthCbRc,
            movements: group.movements
        };
    });

    return {
        processedGroups,
        currentMonthTotals: {
            fbTotal: currentMonthFbTotal,
            cbRcTotal: currentMonthCbRcTotal,
            balance: currentMonthFbTotal - currentMonthCbRcTotal
        }
    };
}
//#endregion

//#region UI Utility Functions
function showLoading() {
    document.getElementById('loadingState').classList.remove('d-none');
    document.getElementById('errorState').classList.add('d-none');
    document.getElementById('inactiveState').classList.add('d-none');
    document.getElementById('mainContent').classList.add('d-none');
}

function hideLoading() {
    document.getElementById('loadingState').classList.add('d-none');
}

function showError(message) {
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorState').classList.remove('d-none');
    document.getElementById('loadingState').classList.add('d-none');
    document.getElementById('inactiveState').classList.add('d-none');
    document.getElementById('mainContent').classList.add('d-none');
}

function showInactiveState() {
    const inactiveEl = document.getElementById('inactiveState');
    const loadingEl = document.getElementById('loadingState');
    const errorEl = document.getElementById('errorState');
    const mainEl = document.getElementById('mainContent');

    // Ocultar todos los demás estados
    if (loadingEl) {
        loadingEl.classList.add('d-none');
        loadingEl.style.display = 'none';
    }
    if (errorEl) {
        errorEl.classList.add('d-none');
        errorEl.style.display = 'none';
    }
    if (mainEl) {
        mainEl.classList.add('d-none');
        mainEl.style.display = 'none';
    }

    // Mostrar estado inactivo
    if (inactiveEl) {
        inactiveEl.classList.remove('d-none');
        inactiveEl.classList.add('d-block');
        inactiveEl.style.setProperty('display', 'block', 'important');
        inactiveEl.style.visibility = 'visible';
        inactiveEl.style.opacity = '1';
    } else {
        console.error('Elemento inactiveState no encontrado en el DOM');
    }
}

function showMainContent() {
    const mainEl = document.getElementById('mainContent');
    const loadingEl = document.getElementById('loadingState');
    const errorEl = document.getElementById('errorState');
    const inactiveEl = document.getElementById('inactiveState');

    // Mostrar contenido principal
    if (mainEl) {
        mainEl.classList.remove('d-none');
        mainEl.style.display = 'block';
    }

    // Ocultar otros estados forzadamente
    if (loadingEl) {
        loadingEl.classList.add('d-none');
        loadingEl.style.display = 'none';
    }
    if (errorEl) {
        errorEl.classList.add('d-none');
        errorEl.style.display = 'none';
    }
    if (inactiveEl) {
        inactiveEl.classList.remove('d-block');
        inactiveEl.classList.add('d-none');
        inactiveEl.style.display = 'none';
    }
}

function switchTab(tabId) {
    activeTab = tabId;

    // Update tab buttons
    const creditoTab = document.getElementById('creditoTab');
    const contadoTab = document.getElementById('contadoTab');

    if (tabId === 'credito') {
        creditoTab.classList.add('tabPillBtnActive');
        creditoTab.classList.remove('tabPillBtnInactive');
        creditoTab.setAttribute('aria-selected', 'true');

        contadoTab.classList.add('tabPillBtnInactive');
        contadoTab.classList.remove('tabPillBtnActive');
        contadoTab.setAttribute('aria-selected', 'false');
    } else {
        contadoTab.classList.add('tabPillBtnActive');
        contadoTab.classList.remove('tabPillBtnInactive');
        contadoTab.setAttribute('aria-selected', 'true');

        creditoTab.classList.add('tabPillBtnInactive');
        creditoTab.classList.remove('tabPillBtnActive');
        creditoTab.setAttribute('aria-selected', 'false');
    }

    // Update tab content
    const creditoContent = document.getElementById('credito');
    const contadoContent = document.getElementById('contado');

    if (tabId === 'credito') {
        creditoContent.classList.add('active', 'show');
        creditoContent.classList.remove('d-none');
        contadoContent.classList.add('d-none');
        contadoContent.classList.remove('active', 'show');
    } else {
        contadoContent.classList.add('active', 'show');
        contadoContent.classList.remove('d-none');
        creditoContent.classList.add('d-none');
        creditoContent.classList.remove('active', 'show');
    }
}

function getCompanyDisplayName(companyName, companyCode) {
    if (companyCode === 1) {
        return 'Zavidoro Corporations';
    } else if (companyCode === 2) {
        return "Merco Sur S. W. Corporations";
    }
    return companyName;
}

function formatCurrency(amount) {
    return `$ ${parseFloat(amount || 0).toFixed(2)}`;
}

// Get mobile-friendly label text
function getMobileLabel(fullText) {
    const isMobile = window.innerWidth <= 620;
    if (!isMobile) return fullText;

    const mobileLabels = {
        'Total de Facturas': 'T. Facturas',
        'Total de Recibos': 'T. Recibos',
        'Total Final': 'T. Final',
        'Total en Compras': 'T. Compras',
        'Compras en el mes': 'C. en el mes'
    };

    return mobileLabels[fullText] || fullText;
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
    });
}

function toggleCompanyExpansion(companyCode, isCredit = true) {
    const expandedSet = isCredit ? expandedCompanies : expandedCashCompanies;
    const isExpanded = expandedSet.has(companyCode);

    if (isExpanded) {
        expandedSet.delete(companyCode);
    } else {
        expandedSet.add(companyCode);
    }

    // Re-render the appropriate tab
    if (isCredit) {
        renderCreditTab();
    } else {
        renderCashTab();
    }
}
//#endregion

//#region Credit Tab Rendering
function renderCreditTab() {

    const creditLimit = parseFloat(datosUsuario.LIMITE_CREDITO || 0);
    const totalUsedCredit = processedMovements.reduce((total, group) => total + group.balance, 0);
    const availableCredit = Math.max(0, creditLimit - totalUsedCredit);
    const usagePercentage = creditLimit > 0 ? (totalUsedCredit / creditLimit) * 100 : 0;

    // Render summary cards
    renderSummaryCards('creditSummaryCards', [
        {
            icon: 'fas fa-building',
            iconBg: 'icon-bg-primary',
            iconColor: 'text-primary',
            title: 'Limite Crédito',
            value: formatCurrency(creditLimit),
            valueBg: 'value-bg-primary'
        },
        {
            icon: 'fas fa-chart-line',
            iconBg: 'icon-bg-warning',
            iconColor: 'text-warning',
            title: 'Utilizado',
            value: formatCurrency(totalUsedCredit),
            valueBg: 'value-bg-warning'
        },
        {
            icon: 'fas fa-wallet',
            iconBg: 'icon-bg-success',
            iconColor: 'text-success',
            title: 'Disponible',
            value: formatCurrency(availableCredit),
            valueBg: 'value-bg-success'
        }
    ]);

    // Render progress bar
    renderProgressBar('creditProgressBar', {
        title: 'Uso de Crédito',
        percentage: usagePercentage,
        label: usagePercentage > 100 ? "100%+ Utilizado" : `${usagePercentage.toFixed(2)}% Utilizado`
    });

    // Render movement table
    renderMovementTable('creditMovementTable', {
        title: 'Historico de Compras',
        data: processedMovements,
        rawData: rawMovements,
        expandedCompanies: expandedCompanies,
        isCredit: true,
        emptyMessage: 'No hay movimientos disponibles'
    });
}

function renderSummaryCards(containerId, cards) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = cards.map(card => `
        <div class="col-6 col-md-6 col-lg-4 summaryCardCol">
            <div class="card bg-dark shadow-sm rounded-3">
                <div class="card-body d-flex flex-column align-items-center justify-content-center text-center p-2 p-md-4">
                    <div class="${card.iconBg} mb-1 mb-md-3 d-flex align-items-center justify-content-center rounded-circle flex-shrink-0 summaryCardIcon">
                        <i class="${card.icon} ${card.iconColor} summaryCardIconSize"></i>
                    </div>
                    <h6 class="fw-bold mb-1 mb-md-2 text-center text-light summaryCardTitle">
                        ${card.title}
                    </h6>
                    <div class="summaryCardValueWrapper ${card.valueBg}">
                        <h3 class="mb-0 fw-bold text-light text-center summaryCardValue">
                            ${card.value}
                        </h3>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

function renderProgressBar(containerId, config) {
    const container = document.getElementById(containerId);
    if (!container) return;

    container.innerHTML = `
        <div class="w-100 progressBarContainer">
            <div class="card border-0 shadow-lg rounded-3 mb-2">
                <div class="card-body p-3 p-md-4 cardDetalCompany">
                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-center mb-3 gap-2">
                        <h6 class="fw-bold mb-0 d-flex align-items-center progressBarTitle">
                            <i class="fas fa-chart-pie me-2 text-primary progressBarIcon"></i>
                            <span>${config.title}</span>
                        </h6>
                        <span class="badge bg-primary rounded-pill px-3 py-2 flex-shrink-0 progressBarLabel">
                            ${config.label}
                        </span>
                    </div>
                    <div class="progress rounded-pill progressBar">
                        <div class="progress-bar bg-primary rounded-pill"
                             role="progressbar"
                             style="width: ${Math.min(config.percentage, 100).toFixed(2)}%; transition: width 0.6s ease-in-out"
                             aria-valuenow="${Math.min(parseFloat(config.percentage.toFixed(2)), 100)}"
                             aria-valuemin="0"
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}
//#endregion

//#region Movement Table Rendering
function renderMovementTable(containerId, config) {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (!config.data || config.data.length === 0) {
        container.innerHTML = `
            <div class="card border-0 shadow rounded-4 overflow-hidden">
                <div class="card-header bg-dark">
                    <h5 class="fw-bold mb-0 text-light d-flex align-items-center">
                        ${config.title}
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="text-center py-5 px-3 emptyStateIconContainer">
                        <div class="text-muted">
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3 emptyStateIcon">
                                <i class="fas fa-info-circle text-secondary emptyStateIconSvg"></i>
                            </div>
                            <p class="mb-0 fw-medium fs-6">${config.emptyMessage}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        return;
    }

    const companyCount = config.data.length;
    let cardWidthClass = '';
    if (companyCount === 1) cardWidthClass = 'card-company-single';
    else if (companyCount === 2) cardWidthClass = 'card-company-double';

    container.innerHTML = `
        <div class="card border-0 shadow rounded-4 overflow-hidden ${cardWidthClass}">
            <div class="card-header bg-dark">
                <h5 class="fw-bold mb-0 text-light d-flex align-items-center">
                    ${config.title}
                </h5>
            </div>
            <div class="card-body p-0">
                <!-- Desktop View -->
                <div class="d-none d-lg-block">
                    <div class="historicoComprasGrid">
                        ${config.data.map(group => renderCompanyCard(group, config)).join('')}
                    </div>
                </div>

                <!-- Mobile View -->
                <div class="d-lg-none">
                    <div class="px-2 py-1">
                        ${config.data.map((group, index) => renderMobileCompanyCard(group, config, index)).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderCompanyCard(group, config) {
    const isExpanded = config.expandedCompanies.has(group.companyCode);
    const displayName = getCompanyDisplayName(group.companyName, group.companyCode);

    return `
        <div class="companyCardGridItem">
            <div class="border-0 shadow-lg overflow-hidden ${isExpanded ? 'companyCardBorderRadiusExpanded' : 'companyCardBorderRadius'}">
                <div class="p-2">
                    <div class="companyCardVertical d-flex flex-column px-3 py-2 mb-2 rounded-4 shadow-sm">
                        <!-- Company Name -->
                        <div class="companyCardTop">
                            <div class="companyNameDisplay">
                                <h6 class="fw-bold text-dark mb-0 companyNameTitle" title="${displayName}">
                                    ${displayName}
                                </h6>
                            </div>
                        </div>

                        <!-- Summary Data + Action Button -->
                        <div class="companyCardBottom">
                            <div class="d-flex align-items-center gap-2 w-100 justify-content-center">
                                ${config.isCredit ? `
                                    <div class="summaryBlock d-flex flex-column align-items-center px-2 py-1 rounded-2 bg-white border border-light shadow">
                                        <span class="ec-color-factura fw-semibold summaryBlockLabel">${getMobileLabel('Total de Facturas')}</span>
                                        <span class="fw-bold ec-color-factura">${formatCurrency(group.fbTotal)}</span>
                                    </div>
                                    <div class="summaryBlock d-flex flex-column align-items-center px-2 py-1 rounded-2 bg-white border border-light shadow">
                                        <span class="ec-color-recibo fw-semibold summaryBlockLabel">${getMobileLabel('Total de Recibos')}</span>
                                        <span class="fw-bold ec-color-recibo">${formatCurrency(group.cbRcTotal)}</span>
                                    </div>
                                    <div class="summaryBlock d-flex flex-column align-items-center px-2 py-1 rounded-2 bg-white border border-light shadow">
                                        <span class="ec-color-total-final fw-semibold summaryBlockLabel">${getMobileLabel('Total Final')}</span>
                                        <span class="fw-bold ec-color-total-final">${formatCurrency(group.balance)}</span>
                                    </div>
                                ` : `
                                    <div class="summaryBlock d-flex flex-column align-items-center px-2 py-1 rounded-2 bg-white border border-light shadow">
                                        <span class="ec-color-compras fw-semibold summaryBlockLabel">${getMobileLabel('Total en Compras')}</span>
                                        <span class="fw-bold ec-color-compras">${formatCurrency(group.balance)}</span>
                                    </div>
                                    <div class="summaryBlock d-flex flex-column align-items-center px-2 py-1 rounded-2 bg-white border border-light shadow">
                                        <span class="ec-color-mes fw-semibold summaryBlockLabel">${getMobileLabel('Compras en el mes')}</span>
                                        <span class="fw-bold ec-color-mes">${formatCurrency(group.currentMonthBalance)}</span>
                                    </div>
                                `}

                                <!-- Action Button -->
                                <div class="companyCardActionButton">
                                    <button type="button"
                                            class="btn btn-primary rounded-3 shadow-sm d-inline-flex align-items-center justify-content-center position-relative eyeToggleButton ${isExpanded ? 'eyeToggleButtonExpanded' : 'eyeToggleButtonNormal'}"
                                            title="${isExpanded ? 'Ocultar detalles' : 'Ver detalles'}"
                                            onclick="toggleCompanyExpansion(${group.companyCode}, ${config.isCredit})">
                                        <div class="d-flex align-items-center justify-content-center eyeToggleIcon ${isExpanded ? 'eyeToggleIconExpanded' : 'eyeToggleIconNormal'}">
                                            <i class="${isExpanded ? 'fas fa-eye-slash' : 'fas fa-eye'} text-light eyeToggleIconSize eyeToggleIconShadow"></i>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Desktop Detail Expansion -->
                ${isExpanded ? renderMovementDetails(group, config) : ''}
            </div>
        </div>
    `;
}
//#endregion

//#region Movement Details and Mobile Rendering
function renderMovementDetails(group, config) {
    const movements = config.rawData.filter(movement => movement.CODEMP === group.companyCode);

    return `
        <div class="d-none d-lg-block col-12 companyDetailExpansion">
            <div class="d-flex flex-column">
                ${movements.map(movement => {
                    const isInvoice = movement.TIPO_COMPROBANTE === 'FB';
                    const isReceipt = movement.TIPO_COMPROBANTE === 'RC' || movement.TIPO_COMPROBANTE === 'CB';
                    const amount = parseFloat(movement.MONTO || movement.TOTAL_EN_DOLARES || '0') || 0;
                    const dateStr = formatDate(movement.FECHA);

                    return `
                        <div class="d-flex align-items-center movementDetailCardDesktop mb-2 ${isInvoice ? 'movementDetailCardInvoice' : isReceipt ? 'movementDetailCardReceipt' : 'movementDetailCardOther'}"
                             tabindex="0">
                            <!-- Left: Icon + Type/Location -->
                            <div class="d-flex align-items-center flex-grow-1 min-width-0 movementDetailCardLeft">
                                <div class="d-flex flex-column align-items-center justify-content-center me-2 movementDetailCardIconCol">
                                    <div class="movementDetailCardIconBg">
                                        <i class="${isInvoice ? `fas fa-file-invoice ${config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado'}` : isReceipt ? 'fas fa-receipt ec-color-recibo' : 'fas fa-file text-dark'} movementDetailCardIcon"></i>
                                    </div>
                                    <span class="fw-bold text-uppercase movementDetailCardTypeLabel ${isInvoice ? (config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado') : isReceipt ? 'ec-color-recibo' : ''}">
                                        ${isInvoice ? 'Factura' : isReceipt ? 'Recibo' : movement.TIPO_COMPROBANTE}
                                    </span>
                                </div>
                                <div class="d-flex flex-column justify-content-center min-width-0 movementDetailCardInfo">
                                    <span class="fw-bold movementDetailCardSucursal ${isInvoice ? (config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado') : 'ec-color-recibo'}">
                                        ${movement.NOMBRE_SUCURSAL}
                                    </span>
                                    <span class="movementDetailCardComprobante fw-bold ${isInvoice ? 'text-dark' : 'text-dark'}">
                                        ${movement.PREFIJO && movement.NUMERO ? `${movement.PREFIJO}-${movement.NUMERO}` : ''}
                                    </span>
                                </div>
                            </div>
                            <!-- Right: Amount + Date -->
                            <div class="d-flex flex-column align-items-end ms-2 movementDetailCardRight">
                                <span class="fw-bold ${isInvoice ? (config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado') : 'ec-color-recibo'} movementDetailCardAmount">
                                    ${formatCurrency(amount)}
                                </span>
                                <span class="px-text-dark fw-bold movementDetailCardDate">
                                    <div class="text-dark">${dateStr}</div>
                                </span>
                            </div>
                            <div class="movementDetailCardDivider"></div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

function renderMobileCompanyCard(group, config, groupIndex) {
    const isExpanded = config.expandedCompanies.has(group.companyCode);
    const displayName = getCompanyDisplayName(group.companyName, group.companyCode);

    return `
        <div class="mb-3">
            <!-- Mobile Company Card -->
            <div class="card border-0 overflow-hidden cardMobileCompany mobileCompanyCard ${isExpanded ? 'mobileCompanyCardRadiusExpanded' : 'mobileCompanyCardRadius'}"
                 style="box-shadow: 1px 1px 1px 4px rgba(0, 0, 0, 0.18)">
                <div class="card-body p-3 p-md-4">
                    <div class="d-flex align-items-start justify-content-between mb-3 mb-md-4">
                        <div class="d-flex align-items-start flex-grow-1 me-2">
                            <div class="border rounded-4 me-2 d-flex align-items-center justify-content-center flex-shrink-0 shadow-sm mobileCompanyIcon mobileCompanyIconBg${groupIndex % 3}">
                                <i class="fas fa-building ${groupIndex % 3 === 0 ? 'text-primary' : groupIndex % 3 === 1 ? 'text-success' : 'text-purple'} mobileCompanyIconFg"></i>
                            </div>
                            <div class="flex-grow-1 min-width-0 me-1">
                                <div class="companyNameDisplay companyNameDisplayMobile">
                                    <h6 class="fw-bold text-dark mb-0 companyNameTitle companyNameTitleMobile" title="${displayName}">
                                        ${displayName}
                                    </h6>
                                </div>
                            </div>
                        </div>
                        <div class="flex-shrink-0 ms-2">
                            <button type="button"
                                    class="btn btn-primary rounded-3 shadow-sm d-inline-flex align-items-center justify-content-center position-relative eyeToggleButton ${isExpanded ? 'eyeToggleButtonExpanded' : 'eyeToggleButtonNormal'}"
                                    title="${isExpanded ? 'Ocultar detalles' : 'Ver detalles'}"
                                    onclick="toggleCompanyExpansion(${group.companyCode}, ${config.isCredit})">
                                <div class="d-flex align-items-center justify-content-center eyeToggleIcon ${isExpanded ? 'eyeToggleIconExpanded' : 'eyeToggleIconNormal'}">
                                    <i class="${isExpanded ? 'fas fa-eye-slash' : 'fas fa-eye'} text-light eyeToggleIconSize eyeToggleIconShadow"></i>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Mobile Summary Grid -->
                    <div class="row g-2 mb-0">
                        ${config.isCredit ? `
                            <div class="col-4">
                                <div class="text-center p-2 bg-white bg-opacity-70 rounded-3 shadow-sm border border-light border-opacity-50 summaryGridCellMobile">
                                    <div class="small ec-color-factura fw-semibold mb-1 d-flex align-items-center justify-content-center summaryGridCellLabelMobile">
                                        <i class="fas fa-file-invoice me-1 ec-color-factura summaryGridCellIcon"></i>
                                        <span>${getMobileLabel('Total de Facturas')}</span>
                                    </div>
                                    <div class="fw-bold ec-color-factura summaryGridCellValueMobile">
                                        ${formatCurrency(group.fbTotal)}
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center p-2 bg-white bg-opacity-70 rounded-3 shadow-sm border border-light border-opacity-50 summaryGridCellMobile">
                                    <div class="small ec-color-recibo fw-semibold mb-1 d-flex align-items-center justify-content-center summaryGridCellLabelMobile">
                                        <i class="fas fa-receipt me-1 ec-color-recibo summaryGridCellIcon"></i>
                                        <span>${getMobileLabel('Total de Recibos')}</span>
                                    </div>
                                    <div class="fw-bold ec-color-recibo summaryGridCellValueMobile">
                                        ${formatCurrency(group.cbRcTotal)}
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center p-2 bg-white bg-opacity-70 rounded-3 shadow-sm border border-light border-opacity-50 summaryGridCellMobile">
                                    <div class="small ec-color-total-final fw-semibold mb-1 d-flex align-items-center justify-content-center summaryGridCellLabelMobile">
                                        <i class="fas fa-calculator me-1 ec-color-total-final summaryGridCellIcon"></i>
                                        <span>${getMobileLabel('Total Final')}</span>
                                    </div>
                                    <div class="fw-bold ec-color-total-final summaryGridCellValueMobile">
                                        ${formatCurrency(group.balance)}
                                    </div>
                                </div>
                            </div>
                        ` : `
                            <div class="col-6">
                                <div class="text-center p-2 bg-white bg-opacity-70 rounded-3 shadow-sm border border-light border-opacity-50 summaryGridCellMobile">
                                    <div class="small ec-color-compras fw-semibold mb-1 d-flex align-items-center justify-content-center summaryGridCellLabelMobile">
                                        <i class="fas fa-shopping-cart me-1 ec-color-compras summaryGridCellIcon"></i>
                                        <span>${getMobileLabel('Total en Compras')}</span>
                                    </div>
                                    <div class="fw-bold ec-color-compras summaryGridCellValueMobile">
                                        ${formatCurrency(group.balance)}
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-white bg-opacity-70 rounded-3 shadow-sm border border-light border-opacity-50 summaryGridCellMobile">
                                    <div class="small ec-color-mes fw-semibold mb-1 d-flex align-items-center justify-content-center summaryGridCellLabelMobile">
                                        <i class="fas fa-calendar-check me-1 ec-color-mes summaryGridCellIcon"></i>
                                        <span>${getMobileLabel('Compras en el mes')}</span>
                                    </div>
                                    <div class="fw-bold ec-color-mes summaryGridCellValueMobile">
                                        ${formatCurrency(group.currentMonthBalance)}
                                    </div>
                                </div>
                            </div>
                        `}
                    </div>
                </div>
            </div>

            <!-- Mobile Detail Expansion -->
            ${isExpanded ? renderMobileMovementDetails(group, config) : ''}
        </div>
    `;
}

function renderMobileMovementDetails(group, config) {
    const movements = config.rawData.filter(movement => movement.CODEMP === group.companyCode);

    return `
        <div class="mobileDetailExpand">
            <div class="d-flex flex-column gap-2">
                ${movements.map(movement => {
                    const isInvoice = movement.TIPO_COMPROBANTE === 'FB';
                    const isReceipt = movement.TIPO_COMPROBANTE === 'RC' || movement.TIPO_COMPROBANTE === 'CB';
                    const amount = parseFloat(movement.MONTO || movement.TOTAL_EN_DOLARES || '0') || 0;
                    const dateStr = formatDate(movement.FECHA);

                    return `
                        <div class="mobileMovementRow ${isInvoice ? 'mobileMovementRowInvoice' : isReceipt ? 'mobileMovementRowReceipt' : 'mobileMovementRowOther'}">
                            <!-- Left: Icon + Label + Location -->
                            <div class="d-flex align-items-center flex-grow-1 min-width-0">
                                <div class="me-2 mobileMovementIconContainer">
                                    <i class="${isInvoice ? `fas fa-file-invoice ${config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado'}` : isReceipt ? 'fas fa-receipt ec-color-recibo' : 'fas fa-file text-secondary'}"></i>
                                </div>
                                <div class="d-flex flex-column min-width-0">
                                    <span class="fw-semibold mobileMovementTypeLabel ${isInvoice ? (config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado') : isReceipt ? 'ec-color-recibo' : ''}">
                                        ${isInvoice ? 'Factura' : isReceipt ? 'Recibo' : movement.TIPO_COMPROBANTE}
                                    </span>
                                    <span class="mobileMovementLocation ${isInvoice ? (config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado') : 'ec-color-recibo'}">
                                        ${movement.NOMBRE_SUCURSAL}
                                    </span>
                                </div>
                            </div>
                            <!-- Right: Amount + Date -->
                            <div class="d-flex flex-column align-items-end ms-2" style="min-width: 90px">
                                <span class="fw-bold mobileMovementAmount ${isInvoice ? (config.isCredit ? 'ec-color-factura' : 'ec-color-factura-contado') : 'ec-color-recibo'}">
                                    ${formatCurrency(amount)}
                                </span>
                                <span class="text-muted mobileMovementDate">
                                    ${dateStr}
                                </span>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}
//#endregion

//#region Cash Tab Rendering
function renderCashTab() {

    const creditLimit = parseFloat(datosUsuario.LIMITE_CREDITO || 0);
    const totalUsedCash = currentMonthCashTotals.balance || 0;
    const availableCashCredit = Math.max(0, creditLimit - totalUsedCash);
    const cashUsagePercentage = creditLimit > 0 ? (totalUsedCash / creditLimit) * 100 : 0;
    const currentMonthName = new Date().toLocaleString('es-ES', { month: 'long' });

    // Render summary cards
    renderSummaryCards('cashSummaryCards', [
        {
            icon: 'fas fa-wallet',
            iconBg: 'icon-bg-primary',
            iconColor: 'text-primary',
            title: 'Limite mensual',
            value: formatCurrency(creditLimit),
            valueBg: 'value-bg-primary'
        },
        {
            icon: 'fas fa-money-check',
            iconBg: 'icon-bg-warning',
            iconColor: 'text-warning',
            title: 'Utilizado ',
            value: formatCurrency(totalUsedCash),
            valueBg: 'value-bg-warning'
        },
        {
            icon: 'fas fa-money-bill-trend-up',
            iconBg: 'icon-bg-success',
            iconColor: 'text-success',
            title: 'Disponible',
            value: formatCurrency(availableCashCredit),
            valueBg: 'value-bg-success'
        }
    ]);

    // Render progress bar
    renderProgressBar('cashProgressBar', {
        title: `Utilizado en el mes de <span class="ms-2 badge bg-warning text-dark fw-bold px-3 py-1 rounded-pill shadow-sm">${currentMonthName}</span>`,
        percentage: cashUsagePercentage,
        label: cashUsagePercentage > 100 ? "100%+ Utilizado" : `${cashUsagePercentage.toFixed(2)}% Utilizado`
    });

    // Render movement table
    renderMovementTable('cashMovementTable', {
        title: 'Historico de Compras',
        data: processedCashMovements,
        rawData: rawCashMovements,
        expandedCompanies: expandedCashCompanies,
        isCredit: false,
        emptyMessage: 'No hay movimientos disponibles'
    });
}
//#endregion

// Function to update labels on window resize
function updateLabelsOnResize() {
    // Update summary block labels
    const summaryLabels = document.querySelectorAll('.summaryBlockLabel');
    summaryLabels.forEach(label => {
        const currentText = label.textContent.trim();
        // Map abbreviated labels back to full text for processing
        const fullTextMap = {
            'T. Facturas': 'Total de Facturas',
            'T. Recibos': 'Total de Recibos',
            'T. Final': 'Total Final',
            'T. Compras': 'Total en Compras',
            'C. en el mes': 'Compras en el mes'
        };

        const fullText = fullTextMap[currentText] || currentText;
        const newText = getMobileLabel(fullText);

        if (newText !== currentText) {
            label.textContent = newText;
        }
    });

    // Update mobile grid labels
    const gridLabels = document.querySelectorAll('.summaryGridCellLabelMobile span');
    gridLabels.forEach(label => {
        const currentText = label.textContent.trim();
        // Map abbreviated labels back to full text for processing
        const fullTextMap = {
            'T. Facturas': 'Total de Facturas',
            'T. Recibos': 'Total de Recibos',
            'T. Final': 'Total Final',
            'T. Compras': 'Total en Compras',
            'C. en el mes': 'Compras en el mes'
        };

        const fullText = fullTextMap[currentText] || currentText;
        const newText = getMobileLabel(fullText);

        if (newText !== currentText) {
            label.textContent = newText;
        }
    });
}

// Add resize event listener
window.addEventListener('resize', updateLabelsOnResize);

// Make functions available globally for onclick handlers
window.switchTab = switchTab;
window.toggleCompanyExpansion = toggleCompanyExpansion;
